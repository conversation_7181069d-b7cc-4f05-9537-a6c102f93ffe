"""
Access Control Module for Telegram Auto-Posting Bot
Handles user authentication, whitelist management, and admin functions.
"""
import json
import os
import logging
from functools import wraps
from typing import List, Set
from telegram import Update
from telegram.ext import CallbackContext

# Configure logging
logger = logging.getLogger(__name__)

# Admin user ID
ADMIN_USER_ID = 1049516929

# Path to approved users file
APPROVED_USERS_FILE = "data/approved_users.json"

class AccessControl:
    """Manages user access control and whitelist functionality."""
    
    def __init__(self):
        self.approved_users: Set[int] = set()
        self.load_approved_users()
    
    def load_approved_users(self) -> None:
        """Load approved users from JSON file."""
        try:
            if os.path.exists(APPROVED_USERS_FILE):
                with open(APPROVED_USERS_FILE, 'r') as f:
                    data = json.load(f)
                    self.approved_users = set(data.get('approved_users', []))
                    # Always ensure admin is in the approved users
                    self.approved_users.add(ADMIN_USER_ID)
                    logger.info(f"Loaded {len(self.approved_users)} approved users")
            else:
                # Create file with admin user if it doesn't exist
                self.approved_users = {ADMIN_USER_ID}
                self.save_approved_users()
                logger.info("Created new approved users file with admin user")
        except Exception as e:
            logger.error(f"Error loading approved users: {e}")
            # Fallback to admin only
            self.approved_users = {ADMIN_USER_ID}
    
    def save_approved_users(self) -> None:
        """Save approved users to JSON file."""
        try:
            # Ensure data directory exists
            os.makedirs(os.path.dirname(APPROVED_USERS_FILE), exist_ok=True)
            
            data = {
                'approved_users': list(self.approved_users),
                'admin_user_id': ADMIN_USER_ID
            }
            
            with open(APPROVED_USERS_FILE, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved {len(self.approved_users)} approved users")
        except Exception as e:
            logger.error(f"Error saving approved users: {e}")
    
    def is_user_approved(self, user_id: int) -> bool:
        """Check if a user is approved to use the bot."""
        return user_id in self.approved_users
    
    def add_user(self, user_id: int) -> bool:
        """Add a user to the approved list."""
        try:
            if user_id not in self.approved_users:
                self.approved_users.add(user_id)
                self.save_approved_users()
                logger.info(f"Added user {user_id} to approved list")
                return True
            return False  # User already approved
        except Exception as e:
            logger.error(f"Error adding user {user_id}: {e}")
            return False
    
    def remove_user(self, user_id: int) -> bool:
        """Remove a user from the approved list (except admin)."""
        try:
            if user_id == ADMIN_USER_ID:
                return False  # Cannot remove admin
            
            if user_id in self.approved_users:
                self.approved_users.remove(user_id)
                self.save_approved_users()
                logger.info(f"Removed user {user_id} from approved list")
                return True
            return False  # User not in list
        except Exception as e:
            logger.error(f"Error removing user {user_id}: {e}")
            return False
    
    def get_approved_users(self) -> List[int]:
        """Get list of all approved users."""
        return list(self.approved_users)
    
    def is_admin(self, user_id: int) -> bool:
        """Check if a user is the admin."""
        return user_id == ADMIN_USER_ID

# Global access control instance
access_control = AccessControl()

def require_approval(func):
    """Decorator to require user approval before executing command."""
    @wraps(func)
    async def wrapper(update: Update, context: CallbackContext, *args, **kwargs):
        try:
            user_id = update.effective_user.id
            
            # Check if user is approved
            if not access_control.is_user_approved(user_id):
                # Send rejection message to unapproved user
                await update.effective_message.reply_text(
                    "🤖 Hello! I am an auto-posting bot with lots of advanced features for "
                    "managing content across multiple Telegram channels. This is a premium "
                    "paid service. Please contact @the_titanium_admin to purchase access."
                )
                logger.warning(f"Unapproved user {user_id} attempted to use command: {func.__name__}")
                return
            
            # User is approved, execute the original function
            return await func(update, context, *args, **kwargs)
            
        except Exception as e:
            logger.error(f"Error in access control wrapper for {func.__name__}: {e}")
            # Send error to admin instead of user
            await send_error_to_admin(context, f"Access control error in {func.__name__}: {e}")
            
            # Send generic message to user
            if update and update.effective_message:
                await update.effective_message.reply_text(
                    "⚠️ An error occurred. The issue has been reported to the administrator."
                )
    
    return wrapper

async def send_error_to_admin(context: CallbackContext, error_message: str) -> None:
    """Send error message to admin user."""
    try:
        await context.bot.send_message(
            chat_id=ADMIN_USER_ID,
            text=f"🚨 *Bot Error Report*\n\n"
                 f"```\n{error_message}\n```",
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Failed to send error to admin: {e}")

async def handle_admin_add_user(update: Update, context: CallbackContext) -> None:
    """Handle admin command to add new approved user."""
    try:
        user_id = update.effective_user.id
        
        # Check if user is admin
        if not access_control.is_admin(user_id):
            await update.message.reply_text("❌ You are not authorized to use this command.")
            return
        
        # Check if user_id argument is provided
        if not context.args or len(context.args) != 1:
            await update.message.reply_text(
                "❌ Usage: /a <user_id>\n"
                "Example: /a 123456789"
            )
            return
        
        try:
            new_user_id = int(context.args[0])
        except ValueError:
            await update.message.reply_text("❌ Invalid user ID. Please provide a numeric user ID.")
            return
        
        # Add user to approved list
        if access_control.add_user(new_user_id):
            await update.message.reply_text(
                f"✅ User {new_user_id} has been added to the approved users list."
            )
            logger.info(f"Admin {user_id} added user {new_user_id} to approved list")
        else:
            await update.message.reply_text(
                f"ℹ️ User {new_user_id} is already in the approved users list."
            )
    
    except Exception as e:
        logger.error(f"Error in admin add user command: {e}")
        await send_error_to_admin(context, f"Error in admin add user command: {e}")
        await update.message.reply_text("⚠️ An error occurred while processing the command.")

async def handle_admin_list_users(update: Update, context: CallbackContext) -> None:
    """Handle admin command to list approved users."""
    try:
        user_id = update.effective_user.id
        
        # Check if user is admin
        if not access_control.is_admin(user_id):
            await update.message.reply_text("❌ You are not authorized to use this command.")
            return
        
        approved_users = access_control.get_approved_users()
        
        if approved_users:
            user_list = "\n".join([f"• {uid}" for uid in sorted(approved_users)])
            await update.message.reply_text(
                f"👥 *Approved Users ({len(approved_users)}):*\n\n{user_list}",
                parse_mode='Markdown'
            )
        else:
            await update.message.reply_text("📝 No approved users found.")
    
    except Exception as e:
        logger.error(f"Error in admin list users command: {e}")
        await send_error_to_admin(context, f"Error in admin list users command: {e}")
        await update.message.reply_text("⚠️ An error occurred while processing the command.")
