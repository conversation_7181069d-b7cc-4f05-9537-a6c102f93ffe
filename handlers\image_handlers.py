"""
Image upload and settings handlers.
"""
import logging
import os
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
from utils.session import get_session_data, set_session_data, clear_session
from utils.keyboards import create_image_settings_keyboard
from access_control import require_approval

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
UPLOADING_IMAGE = 7

@require_approval
async def handle_image_settings(update: Update, context: CallbackContext) -> None:
    """Handle image settings."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get image settings
    image_settings = project_info.get('image_settings', {})
    current_mode = image_settings.get('mode', 'default')
    custom_image_path = image_settings.get('custom_image_path')
    
    # Create message
    message = f"🖼️ *Image Settings*\n\n"
    
    if current_mode == "default":
        message += "Current setting: <b>Default Image</b>\n\n"
        message += "The bot will use a default image for each post based on the content type."
    elif current_mode == "custom":
        message += "Current setting: <b>Custom Image</b>\n\n"
        if custom_image_path and os.path.exists(custom_image_path):
            message += "You have uploaded a custom image that will be used for all posts."
        else:
            message += "No custom image uploaded yet. Please upload an image."
    elif current_mode == "internet":
        message += "Current setting: <b>Internet Image</b>\n\n"
        message += "The bot will search for relevant images on the internet for each post."
    
    # Create keyboard
    keyboard = create_image_settings_keyboard(project_id, current_mode)
    
    await query.edit_message_text(
        message,
        parse_mode="HTML",
        reply_markup=keyboard
    )

@require_approval
async def handle_set_image_mode(update: Update, context: CallbackContext) -> None:
    """Handle setting the image mode."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id and mode from callback data
    parts = query.data.split(':')
    project_id = parts[1]
    mode = parts[2]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get image settings
    image_settings = project_info.get('image_settings', {})
    
    # Update image mode
    image_settings['mode'] = mode
    
    # Save image settings
    db.update_project_image_settings(user_id, project_id, image_settings)
    
    # Show updated image settings
    await handle_image_settings(update, context)

@require_approval
async def handle_upload_image(update: Update, context: CallbackContext) -> int:
    """Start the process of uploading a custom image."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Store project_id in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    
    await query.edit_message_text(
        "📤 Please send me the image you want to use for your posts.\n\n"
        "The image should be in a common format (JPEG, PNG, etc.) and not too large."
    )
    
    return UPLOADING_IMAGE

@require_approval
async def handle_image_upload(update: Update, context: CallbackContext) -> int:
    """Handle image upload."""
    user_id = update.effective_user.id
    
    # Get project_id from user session
    project_id = get_session_data(user_id, 'project_id')
    
    if not project_id:
        await update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END
    
    # Get project info
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END
    
    # Get the largest photo
    photo = update.message.photo[-1]
    
    # Download the photo
    file = await context.bot.get_file(photo.file_id)
    
    # Create directory if it doesn't exist
    os.makedirs(db.IMAGE_DIR, exist_ok=True)
    
    # Save the photo
    file_path = os.path.join(db.IMAGE_DIR, f"{project_id}.jpg")
    await file.download_to_drive(file_path)
    
    # Update image settings
    image_settings = project_info.get('image_settings', {})
    image_settings['mode'] = 'custom'
    image_settings['custom_image_path'] = file_path

    # Save image settings
    db.update_project_image_settings(user_id, project_id, image_settings)
    
    # Create keyboard to go back to image settings
    keyboard = [
        [InlineKeyboardButton("⬅️ Back to Image Settings", callback_data=f"image_settings:{project_id}")]
    ]
    
    await update.message.reply_text(
        "✅ Image uploaded successfully!\n\n"
        "This image will now be used for all posts in this project.",
        reply_markup=InlineKeyboardMarkup(keyboard)
    )
    
    # Clear session data
    clear_session(user_id)
    
    return ConversationHandler.END

@require_approval
async def handle_reset_image(update: Update, context: CallbackContext) -> None:
    """Handle resetting the custom image."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id

    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get image settings
    image_settings = project_info.get('image_settings', {})
    custom_image_path = image_settings.get('custom_image_path')
    
    # Delete the custom image if it exists
    if custom_image_path and os.path.exists(custom_image_path):
        try:
            os.remove(custom_image_path)
        except Exception as e:
            logger.error(f"Error deleting custom image: {e}")
    
    # Reset image settings
    image_settings['custom_image_path'] = None
    
    # Save image settings
    db.update_project_image_settings(user_id, project_id, image_settings)
    
    # Show updated image settings
    await handle_image_settings(update, context)
