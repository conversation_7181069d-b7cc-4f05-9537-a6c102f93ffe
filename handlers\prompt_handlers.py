"""
Custom AI prompt handlers.
"""
import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ConversationHandler

import database as db
import prompt_config
from utils.session import get_session_data, set_session_data, clear_session
from access_control import require_approval

# Enable logging
logger = logging.getLogger(__name__)

# Conversation states
ENTERING_CUSTOM_PROMPT = 9

def escape_markdown(text: str) -> str:
    """
    Escape Markdown special characters for safe display in Telegram messages.

    Args:
        text (str): Text that may contain Markdown characters

    Returns:
        str: Text with Markdown characters escaped
    """
    if not text:
        return text

    # Characters that need to be escaped in Telegram Markdown
    escape_chars = ['*', '_', '`', '[', ']', '{', '}']

    for char in escape_chars:
        text = text.replace(char, f'\\{char}')

    return text

@require_approval
async def handle_edit_prompt(update: Update, context: CallbackContext) -> None:
    """Handle editing AI prompt for a project."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type
    channels = project_info.get('channels', [])
    if not channels:
        await query.edit_message_text(
            "❌ No channels found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    content_type = channels[0].get('content_type')
    if not content_type:
        await query.edit_message_text(
            "❌ Content type not found for this project.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Settings", callback_data=f"project_settings:{project_id}")]
            ])
        )
        return
    
    # Get current prompt (custom or default)
    custom_prompt = db.get_project_custom_prompt(user_id, project_id)
    
    # Get default prompt for comparison
    if content_type == "daily_news_summary":
        default_prompt = prompt_config.get_prompt("daily_news", country="India", current_date="TODAY")
    elif content_type == "cricket_news":
        default_prompt = prompt_config.get_prompt("cricket_news", current_date="TODAY", news_data="[Sample news articles will be inserted here]")
    elif content_type == "health_fitness":
        default_prompt = prompt_config.get_prompt("health_fitness")
    elif content_type == "crypto_prices":
        default_prompt = prompt_config.get_prompt("crypto_prices")
    elif content_type == "custom_content":
        default_prompt = prompt_config.get_prompt("custom_content")
    else:
        default_prompt = "No default prompt available for this content type."
    
    # Create message
    message = f"🤖 *AI Prompt Settings*\n\n"
    message += f"Content Type: *{content_type.replace('_', ' ').title()}*\n\n"
    
    if custom_prompt:
        message += "📝 *Current Custom Prompt:*\n"
        # Truncate long prompts for display and escape Markdown
        display_prompt = custom_prompt[:200] + "..." if len(custom_prompt) > 200 else custom_prompt
        escaped_prompt = escape_markdown(display_prompt)
        message += f"`{escaped_prompt}`\n\n"
        message += "You are using a custom prompt for this project."
    else:
        message += "📋 *Using Default Prompt*\n\n"
        # Truncate long prompts for display and escape Markdown
        display_prompt = default_prompt[:200] + "..." if len(default_prompt) > 200 else default_prompt
        escaped_prompt = escape_markdown(display_prompt)
        message += f"`{escaped_prompt}`\n\n"
        message += "You are using the default system prompt."
    
    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("✏️ Edit Custom Prompt", callback_data=f"start_edit_prompt:{project_id}")],
        [InlineKeyboardButton("📋 View Full Current Prompt", callback_data=f"view_full_prompt:{project_id}")],
    ]
    
    if custom_prompt:
        keyboard.append([InlineKeyboardButton("🔄 Reset to Default", callback_data=f"reset_prompt:{project_id}")])
    
    keyboard.append([InlineKeyboardButton("⬅️ Back to Settings", callback_data=f"project_settings:{project_id}")])
    
    try:
        await query.edit_message_text(
            text=message,
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    except Exception as e:
        logger.error(f"Failed to edit message due to entity parsing error: {e}")
        await query.message.reply_text(
            text=message,
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
        try:
            await query.message.delete()
        except Exception as delete_error:
            logger.warning(f"Could not delete original message: {delete_error}")

@require_approval
async def handle_view_full_prompt(update: Update, context: CallbackContext) -> None:
    """Handle viewing the full current prompt."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Get content type
    channels = project_info.get('channels', [])
    content_type = channels[0].get('content_type') if channels else None
    
    # Get current prompt
    custom_prompt = db.get_project_custom_prompt(user_id, project_id)
    
    if custom_prompt:
        current_prompt = custom_prompt
        prompt_type = "Custom"
    else:
        # Get default prompt
        if content_type == "daily_news_summary":
            current_prompt = prompt_config.get_prompt("daily_news", country="India", current_date="TODAY")
        elif content_type == "cricket_news":
            current_prompt = prompt_config.get_prompt("cricket_news", current_date="TODAY", news_data="[Sample news articles will be inserted here]")
        elif content_type == "health_fitness":
            current_prompt = prompt_config.get_prompt("health_fitness")
        elif content_type == "crypto_prices":
            current_prompt = prompt_config.get_prompt("crypto_prices")
        elif content_type == "custom_content":
            current_prompt = prompt_config.get_prompt("custom_content")
        else:
            current_prompt = "No prompt available for this content type."
        prompt_type = "Default"
    
    # Split long prompts into multiple messages if needed
    max_length = 3500  # Leave room for formatting
    
    if len(current_prompt) <= max_length:
        escaped_prompt = escape_markdown(current_prompt)
        message = f"📋 *{prompt_type} Prompt*\n\n`{escaped_prompt}`"
        
        keyboard = [
            [InlineKeyboardButton("⬅️ Back to Prompt Settings", callback_data=f"edit_prompt:{project_id}")]
        ]
        
        try:
            await query.edit_message_text(
                text=message,
                parse_mode="Markdown",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except Exception as e:
            logger.error(f"Failed to edit message due to entity parsing error: {e}")
            await query.message.reply_text(
                text=message,
                parse_mode="Markdown",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            try:
                await query.message.delete()
            except Exception as delete_error:
                logger.warning(f"Could not delete original message: {delete_error}")
    else:
        # Send first part
        first_part = current_prompt[:max_length]
        escaped_first_part = escape_markdown(first_part)
        message = f"📋 *{prompt_type} Prompt* (Part 1)\n\n`{escaped_first_part}`"
        
        try:
            await query.edit_message_text(
                text=message,
                parse_mode="Markdown",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back to Prompt Settings", callback_data=f"edit_prompt:{project_id}")]
                ])
            )
        except Exception as e:
            logger.error(f"Failed to edit message due to entity parsing error: {e}")
            await query.message.reply_text(
                text=message,
                parse_mode="Markdown",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("⬅️ Back to Prompt Settings", callback_data=f"edit_prompt:{project_id}")]
                ])
            )
            try:
                await query.message.delete()
            except Exception as delete_error:
                logger.warning(f"Could not delete original message: {delete_error}")
        
        # Send remaining parts
        remaining = current_prompt[max_length:]
        part_num = 2
        
        while remaining:
            part = remaining[:max_length]
            remaining = remaining[max_length:]
            
            escaped_part = escape_markdown(part)
            part_message = f"📋 *{prompt_type} Prompt* (Part {part_num})\n\n`{escaped_part}`"
            
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=part_message,
                parse_mode="Markdown"
            )
            part_num += 1

@require_approval
async def handle_start_edit_prompt(update: Update, context: CallbackContext) -> int:
    """Start the process of editing a custom prompt."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Store project_id in user session
    user_id = query.from_user.id
    set_session_data(user_id, 'project_id', project_id)
    
    # Get current custom prompt if exists
    custom_prompt = db.get_project_custom_prompt(user_id, project_id)
    
    message = "✏️ *Edit AI Prompt*\n\n"
    message += "Please send me your custom prompt for AI content generation.\n\n"
    message += "📝 *Guidelines:*\n"
    message += "• Keep it clear and specific\n"
    message += "• Maximum 2000 characters\n"
    message += "• Use variables like {country} or {current_date} if needed\n"
    message += "• Be specific about format and style requirements\n\n"
    
    if custom_prompt:
        display_prompt = custom_prompt[:300] + ('...' if len(custom_prompt) > 300 else '')
        escaped_prompt = escape_markdown(display_prompt)
        message += f"*Current custom prompt:*\n`{escaped_prompt}`\n\n"
    
    message += "Send your new prompt or /cancel to abort:"
    
    # Use a try-catch to handle entity parsing errors
    try:
        await query.edit_message_text(
            text=message,
            parse_mode="Markdown",
            reply_markup=None  # Clear the reply markup to avoid entity conflicts
        )
    except Exception as e:
        # If editing fails due to entity parsing, send a new message instead
        logger.error(f"Failed to edit message due to entity parsing error: {e}")
        await query.message.reply_text(
            text=message,
            parse_mode="Markdown"
        )
        # Delete the original message to avoid confusion
        try:
            await query.message.delete()
        except Exception as delete_error:
            logger.warning(f"Could not delete original message: {delete_error}")
    
    return ENTERING_CUSTOM_PROMPT

@require_approval
async def handle_custom_prompt_input(update: Update, context: CallbackContext) -> int:
    """Handle custom prompt input."""
    user_id = update.effective_user.id
    prompt_text = update.message.text.strip()
    
    # Validate prompt
    if not prompt_text:
        await update.message.reply_text("Please enter a valid prompt.")
        return ENTERING_CUSTOM_PROMPT
    
    if len(prompt_text) > 2000:
        await update.message.reply_text(
            "Prompt is too long. Please keep it under 2000 characters.\n"
            f"Current length: {len(prompt_text)} characters"
        )
        return ENTERING_CUSTOM_PROMPT
    
    # Get session data
    project_id = get_session_data(user_id, 'project_id')
    
    if not project_id:
        await update.message.reply_text("Session data missing. Please start over.")
        return ConversationHandler.END
    
    # Get project info
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await update.message.reply_text("Project not found. It may have been deleted.")
        return ConversationHandler.END
    
    # Save custom prompt
    success = db.set_project_custom_prompt(user_id, project_id, prompt_text)
    
    if success:
        # Create keyboard to go back to prompt settings
        keyboard = [
            [InlineKeyboardButton("⬅️ Back to Prompt Settings", callback_data=f"edit_prompt:{project_id}")]
        ]
        
        await update.message.reply_text(
            "✅ Custom prompt saved successfully!\n\n"
            "Your project will now use this custom prompt for AI content generation.",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )
    else:
        await update.message.reply_text(
            "❌ Failed to save custom prompt. Please try again."
        )
    
    # Clear session data
    clear_session(user_id)
    
    return ConversationHandler.END

@require_approval
async def handle_reset_prompt(update: Update, context: CallbackContext) -> None:
    """Handle resetting prompt to default."""
    query = update.callback_query
    await query.answer()
    
    # Extract project_id from callback data
    project_id = query.data.split(':')[1]
    
    # Get project info
    user_id = query.from_user.id
    projects = db.get_projects(user_id)
    project_info = projects.get(project_id, {})
    
    if not project_info:
        await query.edit_message_text(
            "❌ Project not found. It may have been deleted.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("🏠 Main Menu", callback_data="cmd_cancel")]
            ])
        )
        return
    
    # Reset to default prompt
    success = db.remove_project_custom_prompt(user_id, project_id)
    
    if success:
        await query.edit_message_text(
            "✅ Prompt reset to default successfully!\n\n"
            "Your project will now use the default system prompt for AI content generation.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Prompt Settings", callback_data=f"edit_prompt:{project_id}")]
            ])
        )
    else:
        await query.edit_message_text(
            "❌ Failed to reset prompt. Please try again.",
            reply_markup=InlineKeyboardMarkup([
                [InlineKeyboardButton("⬅️ Back to Prompt Settings", callback_data=f"edit_prompt:{project_id}")]
            ])
        )
